import OpenAI from 'openai';
import * as undici from 'undici';
import { IAIServiceConfig } from '../models/AIServiceConfig';
import { decryptApiKey } from '../utils/encryption';
import { logger } from '../business/logger';
import AIConfigCache from './AIConfigCache';
import ActiveAIConfigManager from './ActiveAIConfigManager';

interface ClientCacheItem {
    client: OpenAI;
    configId: string;
    timestamp: number;
}

class OpenAIClientFactory {
    private clientCache = new Map<string, ClientCacheItem>();
    private readonly CACHE_TTL = 30 * 60 * 1000; // 30分钟
    private readonly CLEANUP_INTERVAL = 10 * 60 * 1000; // 10分钟清理一次

    constructor() {
        // 定期清理过期客户端
        setInterval(() => {
            this.cleanupExpiredClients();
        }, this.CLEANUP_INTERVAL);
    }

    /**
     * 创建OpenAI客户端实例
     */
    static createClient(config: IAIServiceConfig): OpenAI {
        try {
            // 解密API密钥
            const apiKey = decryptApiKey(config.apiKey);
            
            logger.info('=== 创建OpenAI客户端 ===');
            logger.info(`配置名称: ${config.name}`);
            logger.info(`Base URL: ${config.baseURL}`);
            logger.info(`API Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);
            logger.info(`提供商: ${config.provider}`);
            logger.info(`超时时间: ${config.timeout}ms`);
            logger.info(`最大重试: ${config.maxRetries}`);

            // 配置代理支持 - 使用新的 fetchOptions 方式
            let fetchOptions: any = {};
            if (config.proxyConfig?.enabled && config.proxyConfig.url) {
                logger.info(`使用代理: ${config.proxyConfig.url}`);
                const proxyAgent = new undici.ProxyAgent(config.proxyConfig.url);
                fetchOptions = {
                    dispatcher: proxyAgent,
                };
            } else if (process.env.HTTPS_PROXY || process.env.HTTP_PROXY) {
                const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY;
                logger.info(`使用环境变量代理: ${proxyUrl}`);
                const proxyAgent = new undici.ProxyAgent(proxyUrl);
                fetchOptions = {
                    dispatcher: proxyAgent,
                };
            }

            const client = new OpenAI({
                apiKey: apiKey,
                baseURL: config.baseURL,
                timeout: config.timeout,
                maxRetries: config.maxRetries,
                fetchOptions: fetchOptions,
            });

            logger.info('OpenAI客户端创建成功');
            return client;
        } catch (error) {
            logger.error(`创建OpenAI客户端失败: ${error.message}`);
            throw new Error(`创建OpenAI客户端失败: ${error.message}`);
        }
    }

    /**
     * 获取或创建客户端（带缓存）
     */
    async getClient(configId: string): Promise<OpenAI> {
        // 检查缓存
        const cached = this.clientCache.get(configId);
        if (cached && !this.isExpired(cached)) {
            logger.debug(`从缓存获取OpenAI客户端: ${configId}`);
            return cached.client;
        }

        // 从缓存获取配置
        let config = await AIConfigCache.getCachedConfig(configId);
        if (!config) {
            // 如果缓存中没有，从数据库加载
            const AIServiceConfig = (await import('../models/AIServiceConfig')).default;
            config = await AIServiceConfig.findById(configId).select('+apiKey');
            if (!config) {
                throw new Error(`AI服务配置不存在: ${configId}`);
            }
            if (!config.isActive) {
                throw new Error(`AI服务配置已禁用: ${config.name}`);
            }
            // 缓存配置
            await AIConfigCache.setCachedConfig(config);
        }

        // 创建新客户端
        const client = OpenAIClientFactory.createClient(config);
        
        // 缓存客户端
        this.clientCache.set(configId, {
            client,
            configId,
            timestamp: Date.now()
        });

        logger.debug(`创建并缓存OpenAI客户端: ${configId} (${config.name})`);
        return client;
    }

    /**
     * 获取默认客户端（优先使用启用配置）
     */
    async getDefaultClient(): Promise<OpenAI> {
        const configInfo = await this.getDefaultClientWithModel();
        return configInfo.client;
    }

    /**
     * 获取默认客户端和模型信息（优先使用启用配置）
     */
    async getDefaultClientWithModel(): Promise<{
        client: OpenAI;
        modelName: string;
        configName: string;
        isActiveConfig: boolean;
    }> {
        // 首先尝试获取启用配置
        try {
            const activeConfig = await ActiveAIConfigManager.getActiveConfig();
            if (activeConfig && activeConfig.configId && activeConfig.modelId) {
                const configName = (activeConfig.configId as any).name;
                const modelName = (activeConfig.modelId as any).modelName;
                const modelDisplayName = (activeConfig.modelId as any).displayName;

                logger.info(`使用启用配置: ${configName} - ${modelDisplayName}`);

                const client = await this.getClient((activeConfig.configId as any)._id.toString());

                return {
                    client,
                    modelName,
                    configName,
                    isActiveConfig: true
                };
            }
        } catch (error) {
            logger.warn('获取启用配置失败，尝试使用默认配置:', error);
        }

        // 回退到默认配置
        let defaultConfig = await AIConfigCache.getCachedDefaultConfig();

        if (!defaultConfig) {
            // 从数据库获取默认配置
            const AIServiceConfig = (await import('../models/AIServiceConfig')).default;
            defaultConfig = await AIServiceConfig.findOne({
                isDefault: true,
                isActive: true
            }).select('+apiKey');

            if (!defaultConfig) {
                throw new Error('未找到默认的AI服务配置');
            }

            // 缓存默认配置
            await AIConfigCache.setCachedConfig(defaultConfig);
        }

        // 获取默认配置的第一个可用模型
        const AIServiceModel = (await import('../models/AIServiceModel')).default;
        const defaultModel = await AIServiceModel.findOne({
            configId: defaultConfig._id,
            isActive: true
        }).sort({ sortOrder: 1, displayName: 1 });

        if (!defaultModel) {
            throw new Error(`默认配置 ${defaultConfig.name} 没有可用的模型`);
        }

        logger.info(`使用默认配置: ${defaultConfig.name} - ${defaultModel.displayName}`);

        const client = await this.getClient(defaultConfig._id.toString());

        return {
            client,
            modelName: defaultModel.modelName,
            configName: defaultConfig.name,
            isActiveConfig: false
        };
    }

    /**
     * 清除客户端缓存
     */
    clearClientCache(configId?: string): void {
        if (configId) {
            this.clientCache.delete(configId);
            logger.debug(`清除OpenAI客户端缓存: ${configId}`);
        } else {
            this.clientCache.clear();
            logger.debug('清除所有OpenAI客户端缓存');
        }
    }

    /**
     * 测试配置连接
     */
    async testConnection(config: IAIServiceConfig): Promise<{ success: boolean; models?: string[]; error?: string }> {
        try {
            const client = OpenAIClientFactory.createClient(config);
            
            // 尝试获取模型列表
            const response = await client.models.list();
            const models = response.data.map(model => model.id);
            
            logger.info(`配置连接测试成功: ${config.name}, 发现${models.length}个模型`);
            return {
                success: true,
                models
            };
        } catch (error) {
            logger.error(`配置连接测试失败: ${config.name}, 错误: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取客户端缓存统计
     */
    getCacheStats() {
        return {
            clientCacheSize: this.clientCache.size,
            cachedClients: Array.from(this.clientCache.keys())
        };
    }

    /**
     * 检查客户端是否过期
     */
    private isExpired(item: ClientCacheItem): boolean {
        return Date.now() - item.timestamp > this.CACHE_TTL;
    }

    /**
     * 清理过期客户端
     */
    private cleanupExpiredClients(): void {
        let cleanedCount = 0;
        
        for (const [key, item] of this.clientCache.entries()) {
            if (this.isExpired(item)) {
                this.clientCache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            logger.debug(`清理过期OpenAI客户端: ${cleanedCount}个`);
        }
    }
}

// 单例模式
export default new OpenAIClientFactory();
